'use client'
import { useState } from "react"
import { ItemChoose } from "@/components/features/etopfun/ItemChoose"
import { ItemGrid } from "@/components/features/etopfun/ItemGrid"
import  OrderForm  from "@/components/features/etopfun/OrderForm"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Package, ShoppingBag } from "lucide-react"

export default function Etopfun({ items, banks, steamId, rate }) {
    const [selectedItems, setSelectedItems] = useState([])
    const handleItemSelect = (item) => {
        setSelectedItems((prev) => [...prev, item])
    }
    const handleItemDeselect = (item) => {
        setSelectedItems((prev) => prev.filter((i) => i.id !== item.id))
    }
    const totalValue = selectedItems.reduce((sum, item) => sum + item.value, 0)

    return (
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-4">
            {/* Rate Info Bar */}
            <div className="mb-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-800">
                <div className="flex items-center justify-center gap-4 text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Tỷ giá hiện tại:</span>
                    <span className="font-semibold text-blue-600 dark:text-blue-400">{rate}x</span>
                    <span className="text-gray-500">•</span>
                    <span className="text-green-600 dark:text-green-400 font-medium">Auto Bán Shard Etop</span>
                </div>
            </div>

            {/* Main Content */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
                {/* Order Form Section */}
                <div className="lg:col-span-1">
                    <div className="bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 className="font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                                <ShoppingBag className="h-5 w-5 text-orange-500" />
                                Đặt Hàng
                            </h3>
                        </div>
                        <div className="p-4">
                            <ItemChoose
                                selectedItems={selectedItems}
                                onItemDeselect={handleItemDeselect}
                            />
                            <OrderForm
                                selectedItems={selectedItems}
                                totalValue={totalValue}
                                steamId={steamId}
                                banks={banks}
                                rate={rate}
                            />
                        </div>
                    </div>
                </div>

                {/* Item Grid Section */}
                <div className="lg:col-span-3">
                    <div className="bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
                        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 className="font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                                <Package className="h-5 w-5 text-blue-500" />
                                Hòm Đồ ({items.length} items)
                            </h3>
                        </div>
                        <div className="p-4">
                            <ItemGrid
                                items={items}
                                selectedItems={selectedItems}
                                onItemSelect={handleItemSelect}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}