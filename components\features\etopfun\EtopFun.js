'use client'
import { useState } from "react"
import { ItemChoose } from "@/components/features/etopfun/ItemChoose"
import { ItemGrid } from "@/components/features/etopfun/ItemGrid"
import  OrderForm  from "@/components/features/etopfun/OrderForm"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Package, ShoppingBag } from "lucide-react"

export default function Etopfun({ items, banks, steamId, rate }) {
    const [selectedItems, setSelectedItems] = useState([])
    const handleItemSelect = (item) => {
        setSelectedItems((prev) => [...prev, item])
    }
    const handleItemDeselect = (item) => {
        setSelectedItems((prev) => prev.filter((i) => i.id !== item.id))
    }
    const totalValue = selectedItems.reduce((sum, item) => sum + item.value, 0)

    return (
        <div className="max-w-8xl mx-auto px-1 md:px-6 lg:px-8 pt-2 pb-6">
            {/* Hero Section */}
            <div className="text-center mb-6 md:mb-8">
                <h1 className="text-2xl md:text-4xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-2">
                    Auto Bán Shard Etop
                </h1>
                <p className="text-sm md:text-base text-gray-600 dark:text-gray-400">
                    Chọn item và bán tự động với tỷ giá tốt nhất
                </p>
            </div>

            {/* Main Trading Section */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 md:gap-8">
                {/* Order Form Section */}
                <div className="lg:col-span-1">
                    <Card className="border-0 shadow-xl bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm">
                        <CardHeader className="pb-3 md:pb-4 p-4 md:p-6">
                            <CardTitle className="flex items-center space-x-2 md:space-x-3 text-xl md:text-2xl bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                                <ShoppingBag className="h-6 w-6 md:h-7 md:w-7 text-orange-600" />
                                <span>Đặt Hàng Etop</span>
                            </CardTitle>
                            <p className="text-sm md:text-base text-gray-600 dark:text-gray-400">
                                Chọn item và nhập thông tin để bán
                            </p>
                        </CardHeader>
                        <CardContent className="p-4 md:p-6 pt-0">
                            <ItemChoose
                                selectedItems={selectedItems}
                                onItemDeselect={handleItemDeselect}
                            />
                            <OrderForm
                                selectedItems={selectedItems}
                                totalValue={totalValue}
                                steamId={steamId}
                                banks={banks}
                                rate={rate}
                            />
                        </CardContent>
                    </Card>
                </div>

                {/* Item Grid Section */}
                <div className="lg:col-span-2">
                    <Card className="border-0 shadow-xl bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm">
                        <CardHeader className="pb-3 md:pb-4 p-4 md:p-6">
                            <CardTitle className="flex items-center space-x-2 md:space-x-3 text-xl md:text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                <Package className="h-6 w-6 md:h-7 md:w-7 text-blue-600" />
                                <span>Hòm Đồ Etop</span>
                            </CardTitle>
                            <p className="text-sm md:text-base text-gray-600 dark:text-gray-400">
                                Chọn các item bạn muốn bán
                            </p>
                        </CardHeader>
                        <CardContent className="p-4 md:p-6 pt-0">
                            <ItemGrid
                                items={items}
                                selectedItems={selectedItems}
                                onItemSelect={handleItemSelect}
                            />
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}