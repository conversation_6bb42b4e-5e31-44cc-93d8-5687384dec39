import { Badge } from "@/components/ui/badge"
import { Star, Lock } from "lucide-react"

export default function ItemBox({ imageUrl, imageBottomShow, price, status }) {
  return (
    <div
      className="w-20 h-24 sm:w-24 sm:h-28 relative group cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-lg"
      tabIndex={0}
      role="button"
      aria-label={`Item ${imageBottomShow?.name || 'Unknown'} - Giá ${price}`}
    >
      {/* Main Container */}
      <div className="relative w-full h-full rounded-lg overflow-hidden bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 group-hover:border-blue-400 dark:group-hover:border-blue-500 transition-all duration-200 shadow-sm group-hover:shadow-md">
        {/* Image Section */}
        <div className="relative w-full h-16 sm:h-20 bg-gray-100 dark:bg-gray-700">
          <img
            src={imageUrl}
            alt={imageBottomShow?.name || "Item"}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Price Section - Prominent */}
        <div className="absolute bottom-0 left-0 right-0 bg-green-600 text-white p-1 sm:p-1.5">
          <div className="text-xs sm:text-sm font-bold text-center leading-tight">
            {price}
          </div>
        </div>
      </div>

      {/* Hover effect */}
      <div className="absolute inset-0 bg-gradient-to-t from-blue-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg pointer-events-none" />

      {/* Selection ring */}
      <div className="absolute inset-0 rounded-lg ring-2 ring-blue-400 ring-opacity-0 group-hover:ring-opacity-60 transition-all duration-200" />
    </div>
  )
}

