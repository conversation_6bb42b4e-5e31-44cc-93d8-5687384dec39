import { Badge } from "@/components/ui/badge"
import { Star, Lock } from "lucide-react"

export default function ItemBox({ imageUrl, imageBottomShow, price, status }) {
  return (
    <div
      className="w-16 h-20 relative group cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-lg"
      tabIndex={0}
      role="button"
      aria-label={`Item ${imageBottomShow?.name || 'Unknown'} - Giá ${price}`}
    >
      {/* Main Container */}
      <div className="relative w-full h-full rounded-lg overflow-hidden bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 group-hover:border-blue-400 dark:group-hover:border-blue-500 transition-all duration-200 shadow-sm group-hover:shadow-md">
        {/* Image */}
        <div className="relative w-full h-14 bg-gray-100 dark:bg-gray-700">
          <img
            src={imageUrl}
            alt={imageBottomShow?.name || "Item"}
            className="w-full h-full object-cover"
          />

          {/* Status Indicators on Image */}
          <div className="absolute top-1 left-1 flex gap-1">
            {status?.new === 1 && (
              <div className="bg-yellow-500 text-white rounded-full p-0.5 shadow-lg">
                <Star className="h-2 w-2" fill="currentColor" />
              </div>
            )}
            {status?.redlock === 1 && (
              <div className="bg-red-500 text-white rounded-full p-0.5 shadow-lg">
                <Lock className="h-2 w-2" />
              </div>
            )}
          </div>
        </div>

        {/* Bottom section for name and price */}
        <div className="absolute bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-600 p-1">
          {/* Item Name */}
          <div
            className="text-[8px] font-medium text-center leading-tight truncate mb-0.5"
            style={{ color: imageBottomShow?.color || '#374151' }}
            title={imageBottomShow?.name || 'Unknown Item'}
          >
            {imageBottomShow?.name || 'Unknown'}
          </div>

          {/* Price */}
          <div className="bg-green-500 text-white text-[8px] font-bold px-1 py-0.5 rounded text-center">
            {price}
          </div>
        </div>
      </div>

      {/* Hover effect */}
      <div className="absolute inset-0 bg-gradient-to-t from-blue-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg pointer-events-none" />

      {/* Selection ring */}
      <div className="absolute inset-0 rounded-lg ring-2 ring-blue-400 ring-opacity-0 group-hover:ring-opacity-60 transition-all duration-200" />
    </div>
  )
}

