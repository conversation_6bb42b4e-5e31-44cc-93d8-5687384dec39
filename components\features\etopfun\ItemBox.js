import { Badge } from "@/components/ui/badge"
import { Star, Lock } from "lucide-react"

export default function ItemBox({ imageUrl, imageBottomShow, price, status }) {
  return (
    <div
      className="w-24 h-16 relative group cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg"
      tabIndex={0}
      role="button"
      aria-label={`Item ${imageBottomShow?.name || 'Unknown'} - Giá ${price}`}
    >
      {/* Main Image Container */}
      <div className="relative w-full h-full rounded-lg overflow-hidden border-2 border-gray-200 dark:border-gray-700 group-hover:border-blue-400 dark:group-hover:border-blue-500 transition-all duration-200 shadow-sm group-hover:shadow-lg">
        <img
          src={imageUrl}
          alt={imageBottomShow?.name || "Item"}
          width={256}
          height={170}
          className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-110"
        />

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-80 group-hover:opacity-90 transition-opacity duration-200" />

        {/* Item Name */}
        <div className="absolute inset-x-0 bottom-0 p-1">
          <span
            style={{ color: imageBottomShow?.color || '#ffffff' }}
            className="block w-full text-center text-[10px] font-medium leading-tight drop-shadow-sm">
            {imageBottomShow?.name || 'Unknown Item'}
          </span>
        </div>
      </div>

      {/* Price Badge */}
      <Badge
        variant="secondary"
        className="absolute -top-1 -right-1 h-4 leading-4 text-[10px] bg-gradient-to-r from-green-500 to-emerald-500 text-white border-0 px-1.5 py-0 font-semibold shadow-lg group-hover:shadow-xl transition-all duration-200 group-hover:scale-110"
      >
        {price}
      </Badge>

      {/* Status Indicators */}
      <div className="absolute -top-1 -left-1 flex gap-0.5">
        {status?.new === 1 && (
          <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-white rounded-full p-0.5 shadow-lg">
            <Star className="h-2.5 w-2.5" fill="currentColor" />
          </div>
        )}
        {status?.redlock === 1 && (
          <div className="bg-gradient-to-r from-red-500 to-red-600 text-white rounded-full p-0.5 shadow-lg">
            <Lock className="h-2.5 w-2.5" />
          </div>
        )}
      </div>

      {/* Hover Effect Overlay */}
      <div className="absolute inset-0 bg-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg pointer-events-none" />

      {/* Selection Ring Effect */}
      <div className="absolute inset-0 rounded-lg ring-2 ring-blue-400 ring-opacity-0 group-hover:ring-opacity-50 transition-all duration-200" />
    </div>
  )
}

