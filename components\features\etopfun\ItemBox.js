import { Badge } from "@/components/ui/badge"
import { Star, Lock } from "lucide-react"

export default function ItemBox({ imageUrl, imageBottomShow, price, status }) {
  return (
    <div
      className="w-20 h-20 relative group cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded-md transition-all duration-200 hover:scale-105"
      tabIndex={0}
      role="button"
      aria-label={`Item ${imageBottomShow?.name || 'Unknown'} - Giá ${price}`}
    >
      {/* Main Container */}
      <div className="relative w-full h-full rounded-md overflow-hidden bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 group-hover:border-blue-400 dark:group-hover:border-blue-500 transition-all duration-200 shadow-sm group-hover:shadow-md">
        {/* Image */}
        <img
          src={imageUrl}
          alt={imageBottomShow?.name || "Item"}
          className="w-full h-full object-cover"
        />

        {/* Bottom overlay for name */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-1">
          <div
            className="text-[9px] font-medium text-center leading-tight truncate"
            style={{ color: imageBottomShow?.color || '#ffffff' }}
            title={imageBottomShow?.name || 'Unknown Item'}
          >
            {imageBottomShow?.name || 'Unknown'}
          </div>
        </div>
      </div>

      {/* Price Badge */}
      <div className="absolute -top-1 -right-1 bg-green-500 text-white text-[9px] font-bold px-1.5 py-0.5 rounded-full shadow-md min-w-[20px] text-center">
        {price}
      </div>

      {/* Status Indicators */}
      <div className="absolute -top-1 -left-1 flex gap-0.5">
        {status?.new === 1 && (
          <div className="bg-yellow-500 text-white rounded-full p-0.5 shadow-md">
            <Star className="h-2 w-2" fill="currentColor" />
          </div>
        )}
        {status?.redlock === 1 && (
          <div className="bg-red-500 text-white rounded-full p-0.5 shadow-md">
            <Lock className="h-2 w-2" />
          </div>
        )}
      </div>

      {/* Hover overlay */}
      <div className="absolute inset-0 bg-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-md" />
    </div>
  )
}

