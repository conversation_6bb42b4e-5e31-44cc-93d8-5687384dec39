"use client"

import { orderEtopfun } from "@/actions/order"
import { useState, useEffect, useCallback } from "react"
import { Input } from "@/components/ui/input"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useFormStatus } from "react-dom"
import { Button } from "@/components/ui/button"
import { useFormState } from "react-dom"
import { Label } from "@/components/ui/label"
import { orderSchemaEtop } from "@/validations/orderEtopfun.schema"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import OrderInfo from "@/components/features/orderinfo/OrderInfo"
import { fetchUserEtopfun } from "@/lib/http"
import { DollarSign, Loader2, CheckCircle } from "lucide-react"

function SubmitButton({ disabled = false }) {
  const { pending } = useFormStatus()
  const isDisabled = pending || disabled

  return (
    <Button
      type="submit"
      className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-semibold py-2.5 md:py-3 text-sm md:text-base transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
      disabled={isDisabled}
    >
      {pending ? (
        <>
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          Đang xử lý...
        </>
      ) : (
        <>
          <CheckCircle className="h-4 w-4 mr-2" />
          Đặt Hàng Ngay
        </>
      )}
    </Button>
  )
}

export default function OrderForm({ selectedItems, steamId, totalValue, banks, rate }) {
  const defaultBankNo = banks.length > 0 ? banks[0].bankNo : ""
  const [nickName, setNickname] = useState('');
  const createOrder = orderEtopfun.bind(null, selectedItems)
  const [state, formAction] = useFormState(createOrder, null)
  const [selectedBank, setSelectedBank] = useState(defaultBankNo)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const {
    register,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(orderSchemaEtop),
  })
  const handleModalClose = useCallback(() => {
    setIsModalOpen(false)
    formAction(null)
  }, [formAction])

  useEffect(() => {
    if (state?.success) {
      setIsModalOpen(true)
    }
  }, [state?.success])

  const setId = async (steamId) => {
    setNickname('loading...');
    const data = await fetchUserEtopfun(steamId);
    if (data) {
      setNickname(data.name);
      // Auto save steamId to cookie
      document.cookie = `steamIdEtop=${steamId}; path=/; max-age=${60 * 60 * 24 * 30}`; // 30 days
    } else {
      setNickname("")
    }
  }
  useEffect(() => {
    if (steamId) {
      setId(steamId);
    }
  }, [steamId]);
  let amount = 0
  const coin100 = Math.round(totalValue * 100)
  if (coin100 < 1000) {
    amount = coin100 * 10 * rate
  } else amount = Math.round(coin100 * rate / 100.0) * 1000

  const hasSelectedItems = selectedItems.length > 0

  return (
    <>
      <div className="space-y-4">
        {/* Order Summary */}
        {totalValue > 0 && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Tổng Giá Trị</span>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-600 dark:text-gray-400">Value:</span>
                <span className="font-semibold text-orange-600">{totalValue.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 dark:text-gray-400">Nhận được:</span>
                <span className="font-bold text-green-600">{amount.toLocaleString()} VND</span>
              </div>
            </div>
          </div>
        )}

        {/* Order Form */}
        <form action={formAction} className="space-y-3">
          {/* Steam ID Input */}
          <div className="space-y-1">
            <Label htmlFor="steamId" className="text-sm font-medium">Steam ID</Label>
            <Input
              id="steamId"
              defaultValue={steamId}
              type="text"
              {...register("steamId")}
              onBlur={(e) => setId(e.target.value)}
              placeholder="Nhập Steam ID"
              className="h-9 text-sm"
            />
            {errors.steamId && <p className="text-xs text-red-500">{errors.steamId.message}</p>}
            {state?.errors?.steamId && <p className="text-xs text-red-500">{state.errors.steamId[0]}</p>}
          </div>

          {/* Nickname Input */}
          <div className="space-y-1">
            <Label htmlFor="nickName" className="text-sm font-medium">Nickname</Label>
            <div className="relative">
              <Input
                type="text"
                {...register("nickName")}
                value={nickName}
                readOnly
                placeholder="Tự động hiển thị"
                className="h-9 text-sm bg-gray-50 dark:bg-slate-700"
              />
              {nickName === 'loading...' && (
                <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-3 w-3 animate-spin text-blue-500" />
              )}
            </div>
            {errors.nickName && <p className="text-xs text-red-500">{errors.nickName.message}</p>}
            {state?.errors?.nickName && <p className="text-xs text-red-500">{state.errors.nickName[0]}</p>}
          </div>

          {/* Bank Selection */}
          <div className="space-y-1">
            <Label htmlFor="bankNo" className="text-sm font-medium">Ngân Hàng</Label>
            <Select {...register("bankNo")} value={selectedBank} onValueChange={setSelectedBank}>
              <SelectTrigger className="h-9 text-sm">
                <SelectValue placeholder="Chọn ngân hàng" />
              </SelectTrigger>
              <SelectContent>
                {banks.map((bank) => (
                  <SelectItem disabled={!bank.active} key={bank.bankNo} value={bank.bankNo}>
                    <span className="text-sm">{bank.bankName}</span>
                    {!bank.active && <span className="text-xs text-red-500 ml-1">(Tạm ngưng)</span>}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.bankNo && <p className="text-xs text-red-500">{errors.bankNo.message}</p>}
            {state?.errors?.bankNo && <p className="text-xs text-red-500">{state.errors.bankNo[0]}</p>}
          </div>

          {/* Error Messages */}
          {state?.errorServer && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-2">
              <p className="text-xs text-red-600 dark:text-red-400">{state.message}</p>
            </div>
          )}

          {/* Submit Button */}
          <div className="pt-2">
            <SubmitButton disabled={!hasSelectedItems} />
            {!hasSelectedItems && (
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-center">
                Chọn item để tiếp tục
              </p>
            )}
          </div>
        </form>
      </div>
      <Dialog open={isModalOpen} onOpenChange={handleModalClose}>
        {state?.success && (
          <DialogContent className="max-w-lg sm:max-w-xl md:max-w-3xl lg:max-w-5xl sm:rounded-lg rounded-none sm:h-auto h-screen w-full sm:w-auto p-4">
            <DialogHeader>
              <DialogTitle className="text-lg md:text-xl font-semibold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                Thanh toán cho đơn hàng {state?.data?.content}
              </DialogTitle>
            </DialogHeader>
            <div className="max-h-full overflow-y-auto">
              <OrderInfo
                data={state?.data}
                steamInfo={state?.data?.steamAvatar && state?.data?.steamName ? {
                  steamId: state?.data?.steamId,
                  steamName: state?.data?.steamName,
                  steamAvatar: state?.data?.steamAvatar,
                  steamLevel: state?.data?.steamLevel,
                  coin: state?.data?.coin,
                } : null}
              />
            </div>
          </DialogContent>
        )}
      </Dialog>
    </>

  )
}

