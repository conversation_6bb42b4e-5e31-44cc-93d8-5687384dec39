"use client";
import { useState, useEffect } from "react";

const STORAGE_KEY = 'shared_steam_ids';

export function useSharedSteamIds() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [steamIds, setSteamIds] = useState([]);

  // Helper function to safely get from localStorage
  const getStoredValue = (key) => {
    if (typeof window === 'undefined') return null;
    try {
      const value = localStorage.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.warn('Error reading from localStorage:', error);
      return null;
    }
  };

  // Helper function to safely set to localStorage
  const setStoredValue = (key, value) => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.warn('Error writing to localStorage:', error);
    }
  };

  // Validate Steam ID (must be 17 digits and start with 765)
  const isValidSteamId = (steamId) => {
    if (!steamId || typeof steamId !== 'string') return false;

    // Remove any whitespace
    const cleanSteamId = steamId.trim();

    // Check if it's exactly 17 digits and starts with 765
    const steamIdRegex = /^765\d{14}$/;
    return steamIdRegex.test(cleanSteamId);
  };

  // Migrate data from old storage keys
  const migrateOldData = () => {
    const buyCoinIds = getStoredValue('buycoin_steam_ids') || [];
    const sellCoinIds = getStoredValue('sellcoin_steam_ids') || [];

    // Combine, filter valid Steam IDs, and deduplicate
    const allIds = [...new Set([...buyCoinIds, ...sellCoinIds])];
    const validIds = allIds.filter(id => isValidSteamId(id));

    if (validIds.length > 0) {
      setStoredValue(STORAGE_KEY, validIds);

      // Clean up old storage
      if (typeof window !== 'undefined') {
        try {
          localStorage.removeItem('buycoin_steam_ids');
          localStorage.removeItem('sellcoin_steam_ids');
          console.log(`✅ Đã chuyển ${validIds.length} Steam ID hợp lệ sang bộ nhớ chung`);
          if (allIds.length > validIds.length) {
            console.warn(`⚠️ Đã loại bỏ ${allIds.length - validIds.length} Steam ID không hợp lệ`);
          }
        } catch (error) {
          console.warn('Error cleaning up old storage:', error);
        }
      }

      return validIds;
    }

    return [];
  };

  // Load stored data on mount
  useEffect(() => {
    let storedSteamIds = getStoredValue(STORAGE_KEY);

    // If no shared data exists, try to migrate from old storage
    if (!storedSteamIds || storedSteamIds.length === 0) {
      storedSteamIds = migrateOldData();
    } else {
      // Validate existing stored Steam IDs and filter out invalid ones
      const validIds = storedSteamIds.filter(id => isValidSteamId(id));
      if (validIds.length !== storedSteamIds.length) {
        console.warn(`⚠️ Đã loại bỏ ${storedSteamIds.length - validIds.length} Steam ID không hợp lệ khỏi bộ nhớ`);
        setStoredValue(STORAGE_KEY, validIds);
        storedSteamIds = validIds;
      }
    }

    setSteamIds(storedSteamIds || []);
    setIsLoaded(true);
  }, []);

  // Add new Steam ID
  const addSteamId = (steamId) => {
    if (!steamId) return false;

    // Validate Steam ID format
    if (!isValidSteamId(steamId)) {
      console.warn('⚠️ Steam ID không hợp lệ. Steam ID phải có 17 chữ số và bắt đầu bằng 765');
      return false;
    }

    // Check if already exists
    if (steamIds.includes(steamId.trim())) {
      console.warn('⚠️ Steam ID đã tồn tại');
      return false;
    }

    const cleanSteamId = steamId.trim();
    const updatedSteamIds = [...steamIds, cleanSteamId];
    setSteamIds(updatedSteamIds);
    setStoredValue(STORAGE_KEY, updatedSteamIds);
    return true;
  };

  // Remove Steam ID
  const removeSteamId = (steamId) => {
    const updatedSteamIds = steamIds.filter(id => id !== steamId);
    setSteamIds(updatedSteamIds);
    setStoredValue(STORAGE_KEY, updatedSteamIds);
  };

  // Clear all data
  const clearAllData = () => {
    setSteamIds([]);
    setStoredValue(STORAGE_KEY, []);
  };

  return {
    isLoaded,
    steamIds,
    addSteamId,
    removeSteamId,
    clearAllData,
    isValidSteamId
  };
}
