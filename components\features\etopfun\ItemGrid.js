'use client'
import ItemBox from '@/components/features/etopfun/ItemBox'
import { Search, Filter, Package } from "lucide-react"
import { useState } from "react"
import { Input } from "@/components/ui/input"

export function ItemGrid({ items, selectedItems, onItemSelect }) {
  const [searchTerm, setSearchTerm] = useState("")
  const [sortBy, setSortBy] = useState("default")

  const availableItems = items.filter(item =>
    !selectedItems.some(selectedItem => selectedItem.id === item.id)
  )

  // Filter items based on search term
  const filteredItems = availableItems.filter(item =>
    item.imageBottomShow?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.value?.toString().includes(searchTerm)
  )

  // Sort items
  const sortedItems = [...filteredItems].sort((a, b) => {
    switch (sortBy) {
      case "price-high":
        return b.value - a.value
      case "price-low":
        return a.value - b.value
      case "name":
        return (a.imageBottomShow?.name || "").localeCompare(b.imageBottomShow?.name || "")
      default:
        return 0
    }
  })

  return (
    <div className="space-y-4">
      {/* Search and Filter Section */}
      <div className="mb-4">
        <div className="flex flex-col sm:flex-row gap-2 mb-3">
          {/* Search Input */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Tìm kiếm item..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-9 text-sm"
            />
          </div>

          {/* Sort Dropdown */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-2 bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 h-9"
          >
            <option value="default">Mặc định</option>
            <option value="price-high">Giá cao → thấp</option>
            <option value="price-low">Giá thấp → cao</option>
            <option value="name">Tên A → Z</option>
          </select>
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>Hiển thị {sortedItems.length} / {availableItems.length} item</span>
          {searchTerm && (
            <button
              onClick={() => setSearchTerm("")}
              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
            >
              Xóa bộ lọc
            </button>
          )}
        </div>
      </div>

      {/* Items Grid */}
      <div className="border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-slate-900/50">
        {sortedItems.length === 0 ? (
          <div className="text-center py-8">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-3" />
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
              {searchTerm ? "Không tìm thấy item" : "Không có item nào"}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-500">
              {searchTerm ? "Thử thay đổi từ khóa tìm kiếm" : "Hòm đồ hiện tại trống"}
            </p>
          </div>
        ) : (
          <div className='p-3 grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 lg:grid-cols-10 xl:grid-cols-12 gap-3 overflow-auto custom-scrollbar max-h-[400px]'>
            {sortedItems.map((item) => (
              <div
                key={item.id}
                onClick={() => onItemSelect(item)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault()
                    onItemSelect(item)
                  }
                }}
                className="flex justify-center"
              >
                <ItemBox
                  imageUrl={item.image}
                  imageBottomShow={item.imageBottomShow}
                  price={item.value}
                  status={item.status}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}