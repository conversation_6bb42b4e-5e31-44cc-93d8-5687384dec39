'use client'
import ItemBox from '@/components/features/etopfun/ItemBox'
import { Search, Filter, Package } from "lucide-react"
import { useState } from "react"
import { Input } from "@/components/ui/input"

export function ItemGrid({ items, selectedItems, onItemSelect }) {
  const [searchTerm, setSearchTerm] = useState("")
  const [sortBy, setSortBy] = useState("default")

  const availableItems = items.filter(item =>
    !selectedItems.some(selectedItem => selectedItem.id === item.id)
  )

  // Filter items based on search term
  const filteredItems = availableItems.filter(item =>
    item.imageBottomShow?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.value?.toString().includes(searchTerm)
  )

  // Sort items
  const sortedItems = [...filteredItems].sort((a, b) => {
    switch (sortBy) {
      case "price-high":
        return b.value - a.value
      case "price-low":
        return a.value - b.value
      case "name":
        return (a.imageBottomShow?.name || "").localeCompare(b.imageBottomShow?.name || "")
      default:
        return 0
    }
  })

  return (
    <div className="space-y-4">
      {/* Search and Filter Section */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-3 md:p-4 rounded-xl border border-blue-200 dark:border-blue-800">
        <div className="flex flex-col sm:flex-row gap-3">
          {/* Search Input */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Tìm kiếm item..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white/80 dark:bg-slate-800/80 border-gray-200 dark:border-gray-700"
            />
          </div>

          {/* Sort Dropdown */}
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="pl-10 pr-8 py-2 bg-white/80 dark:bg-slate-800/80 border border-gray-200 dark:border-gray-700 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="default">Mặc định</option>
              <option value="price-high">Giá cao → thấp</option>
              <option value="price-low">Giá thấp → cao</option>
              <option value="name">Tên A → Z</option>
            </select>
          </div>
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between mt-3 pt-3 border-t border-blue-200 dark:border-blue-700">
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <Package className="h-4 w-4" />
            <span>
              Hiển thị {sortedItems.length} / {availableItems.length} item
            </span>
          </div>
          {searchTerm && (
            <button
              onClick={() => setSearchTerm("")}
              className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
            >
              Xóa bộ lọc
            </button>
          )}
        </div>
      </div>

      {/* Items Grid */}
      <div className="bg-white/50 dark:bg-slate-800/50 p-3 md:p-4 rounded-xl border border-gray-200 dark:border-gray-700">
        {sortedItems.length === 0 ? (
          <div className="text-center py-12">
            <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
              {searchTerm ? "Không tìm thấy item" : "Không có item nào"}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-500">
              {searchTerm ? "Thử thay đổi từ khóa tìm kiếm" : "Hòm đồ hiện tại trống"}
            </p>
          </div>
        ) : (
          <div className='flex flex-row flex-wrap content-start gap-2 overflow-auto custom-scrollbar h-[300px] md:h-[480px]'>
            {sortedItems.map((item) => (
              <div
                key={item.id}
                onClick={() => onItemSelect(item)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault()
                    onItemSelect(item)
                  }
                }}
                className="cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-lg focus-within:scale-105 focus-within:shadow-lg"
              >
                <ItemBox
                  imageUrl={item.image}
                  imageBottomShow={item.imageBottomShow}
                  price={item.value}
                  status={item.status}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}