
'use client'
import ItemBox from "@/components/features/etopfun/ItemBox";
import { X, Package2 } from "lucide-react";

export function ItemChoose({ selectedItems, onItemDeselect }) {
    return (
        <div className="mb-4">
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-slate-900/50">
                <div className="p-3 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <Package2 className="h-4 w-4 text-orange-500" />
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                Item Đã Chọn
                            </span>
                        </div>
                        {selectedItems.length > 0 && (
                            <span className="bg-orange-100 dark:bg-orange-900/50 text-orange-700 dark:text-orange-300 px-2 py-1 rounded-full text-xs font-medium">
                                {selectedItems.length}
                            </span>
                        )}
                    </div>
                </div>

                <div className="p-3">
                    {selectedItems.length === 0 ? (
                        <div className="text-center py-4">
                            <Package2 className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                                Chưa có item nào được chọn
                            </p>
                        </div>
                    ) : (
                        <div className="grid grid-cols-2 gap-2 max-h-40 overflow-auto custom-scrollbar">
                            {selectedItems.map((item) => (
                                <div
                                    key={item.id}
                                    onClick={() => onItemDeselect(item)}
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter' || e.key === ' ') {
                                            e.preventDefault()
                                            onItemDeselect(item)
                                        }
                                    }}
                                    className="relative cursor-pointer group"
                                    role="button"
                                    tabIndex={0}
                                    aria-label={`Xóa item ${item.imageBottomShow?.name || 'Unknown'}`}
                                >
                                    <ItemBox
                                        imageUrl={item.image}
                                        imageBottomShow={item.imageBottomShow}
                                        price={item.value}
                                        status={item.status}
                                    />
                                    <div className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-0.5 opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 transition-opacity duration-200">
                                        <X className="h-2.5 w-2.5" />
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}