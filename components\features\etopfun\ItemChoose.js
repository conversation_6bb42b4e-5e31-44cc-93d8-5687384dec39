
'use client'
import ItemBox from "@/components/features/etopfun/ItemBox";
import { X, Package2 } from "lucide-react";

export function ItemChoose({ selectedItems, onItemDeselect }) {
    return (
        <div className="space-y-3 md:space-y-4">
            <div className="bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 p-3 md:p-4 rounded-xl border border-orange-200 dark:border-orange-800">
                <div className="flex items-center gap-2 mb-3">
                    <Package2 className="h-5 w-5 text-orange-600" />
                    <h3 className="text-base md:text-lg font-semibold text-gray-800 dark:text-gray-200">
                        Item Đã Chọn
                    </h3>
                    {selectedItems.length > 0 && (
                        <span className="ml-auto bg-orange-100 dark:bg-orange-900/50 text-orange-800 dark:text-orange-200 px-2 py-1 rounded-full text-xs font-medium">
                            {selectedItems.length} item
                        </span>
                    )}
                </div>

                {selectedItems.length === 0 ? (
                    <div className="text-center py-6 md:py-8">
                        <Package2 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                            Chưa có item nào được chọn
                        </p>
                        <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                            Nhấp vào item bên phải để thêm
                        </p>
                    </div>
                ) : (
                    <div className="flex flex-wrap gap-2 overflow-auto custom-scrollbar max-h-48 md:max-h-52">
                        {selectedItems.map((item) => (
                            <div
                                key={item.id}
                                onClick={() => onItemDeselect(item)}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter' || e.key === ' ') {
                                        e.preventDefault()
                                        onItemDeselect(item)
                                    }
                                }}
                                className="relative cursor-pointer group transition-all duration-200 hover:scale-105 focus-within:scale-105"
                                role="button"
                                tabIndex={0}
                                aria-label={`Xóa item ${item.imageBottomShow?.name || 'Unknown'}`}
                            >
                                <ItemBox
                                    imageUrl={item.image}
                                    imageBottomShow={item.imageBottomShow}
                                    price={item.value}
                                    status={item.status}
                                />
                                <div className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 transition-opacity duration-200">
                                    <X className="h-3 w-3" />
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    )
}